using Core.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class Transaction : ICompanyEntity
    {
        [Key]
        public int TransactionID { get; set; }
        public int MemberID { get; set; }
        public int? ProductID { get; set; }
        public int CompanyID { get; set; }
        public decimal Amount { get; set; }
        public decimal UnitPrice { get; set; }
        public string TransactionType { get; set; }
        public DateTime TransactionDate { get; set; }
        public int Quantity { get; set; }
        public bool IsPaid { get; set; }

        // Soft Delete alanları
        public bool IsActive { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? DeletedDate { get; set; }
    }
}
