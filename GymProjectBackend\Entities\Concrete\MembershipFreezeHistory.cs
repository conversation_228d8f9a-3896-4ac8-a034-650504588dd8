using Core.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class MembershipFreezeHistory : ICompanyEntity
    {
        [Key]
        public int FreezeHistoryID { get; set; }
        public int MembershipID { get; set; }
        public int CompanyID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime PlannedEndDate { get; set; }
        public DateTime? ActualEndDate { get; set; }
        public int FreezeDays { get; set; }
        public int? UsedDays { get; set; }
        public string CancellationType { get; set; }
        public DateTime CreationDate { get; set; }
    }
}
